<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Système de Gestion des Permis de Travail - Coordinateur</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"/>
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --primary-light: #93c5fd;
            --secondary: #4f46e5;
            --accent: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --sidebar-width: 280px;
            --topbar-height: 70px;
            --sidebar-collapsed-width: 80px;
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-full: 9999px;
            --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-900);
            display: flex;
            min-height: 100vh;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--gray-900);
            color: var(--gray-100);
            position: fixed;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            transition: var(--transition-slow);
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.05);
            overflow: hidden;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .menu-title,
        .sidebar.collapsed .menu-text,
        .sidebar.collapsed .menu-badge,
        .sidebar.collapsed .user-info,
        .sidebar.collapsed .user-actions {
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: absolute;
        }

        .sidebar.collapsed .menu-link {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar.collapsed .menu-icon {
            margin-right: 0;
            font-size: 24px;
        }

        .sidebar-header {
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            min-height: var(--topbar-height);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: white;
            white-space: nowrap;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            flex-shrink: 0;
        }

        .logo-text {
            transition: var(--transition-slow);
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--gray-400);
            font-size: 20px;
            cursor: pointer;
            transition: var(--transition);
            padding: 8px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-700) var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-menu::-webkit-scrollbar-track {
            background: var(--gray-800);
        }

        .sidebar-menu::-webkit-scrollbar-thumb {
            background-color: var(--gray-700);
            border-radius: var(--radius-full);
        }

        .menu-group {
            margin-bottom: 24px;
        }

        .menu-title {
            padding: 0 24px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--gray-400);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: var(--transition-slow);
        }

        .menu-items {
            list-style: none;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-300);
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .menu-link:hover {
            background-color: var(--gray-800);
            color: white;
        }

        .menu-link.active {
            background-color: rgba(37, 99, 235, 0.1);
            border-left-color: var(--primary);
            color: white;
        }

        .menu-link.active .menu-icon {
            color: var(--primary-light);
        }

        .menu-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            transition: var(--transition);
            flex-shrink: 0;
        }

        .menu-text {
            transition: var(--transition-slow);
            white-space: nowrap;
        }

        .sidebar-footer {
            padding: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: auto;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .user-profile:hover {
            background-color: var(--gray-800);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
        }

        .user-info {
            flex: 1;
            min-width: 0;
            transition: var(--transition-slow);
        }

        .user-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 12px;
            color: var(--gray-400);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            transition: var(--transition-slow);
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 18px;
            height: 18px;
            background-color: var(--danger);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
            color: white;
            border: 2px solid var(--gray-900);
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition-slow);
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Top Navigation */
        .topbar {
            height: var(--topbar-height);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: var(--shadow-sm);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .topbar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--gray-500);
            font-size: 24px;
            cursor: pointer;
            transition: var(--transition);
            display: none;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
        }

        .menu-toggle:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .breadcrumbs {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .breadcrumb-item:not(:last-child)::after {
            content: '/';
            color: var(--gray-300);
            margin-left: 8px;
        }

        .breadcrumb-item.active {
            color: var(--gray-700);
            font-weight: 500;
        }

        .topbar-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .topbar-action {
            position: relative;
            cursor: pointer;
        }

        .action-btn-lg {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
            border: none;
            position: relative;
        }

        .action-btn-lg:hover {
            background-color: var(--gray-100);
            color: var(--gray-700);
        }

        .action-btn-lg .notification-badge {
            border-color: white;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            width: 280px;
            background-color: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-xl);
            padding: 8px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition);
        }

        .user-dropdown.active {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 12px 16px;
            border-bottom: 1px solid var(--gray-100);
            margin-bottom: 8px;
        }

        .dropdown-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray-900);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: var(--radius-sm);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: var(--gray-50);
            color: var(--primary);
        }

        .dropdown-item i {
            margin-right: 12px;
            font-size: 18px;
            width: 20px;
            color: var(--gray-500);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--gray-100);
            margin: 8px 0;
        }
        
        /* Settings Styles */
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--gray-800);
            cursor: pointer;
            margin-bottom: 4px;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            border: 2px solid var(--primary);
            border-radius: 4px;
            cursor: pointer;
        }
        
        .form-text {
            font-size: 13px;
            color: var(--gray-500);
            margin-top: 4px;
            margin-left: 28px;
            margin-bottom: 16px;
        }
        
        .profile-section {
            background: white;
            border-radius: var(--radius-lg);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }
        
        .profile-section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }
        
        .profile-section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }
        
        .profile-section-body {
            padding: 24px;
        }
        
        .profile-actions {
            display: flex;
            justify-content: flex-end;
            padding-top: 16px;
            margin-top: 24px;
            border-top: 1px solid var(--gray-200);
        }

        /* Content Area */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            background-color: var(--gray-50);
        }

        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .content-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .content-subtitle {
            font-size: 16px;
            color: var(--gray-600);
            margin-top: 8px;
        }

        .content-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid transparent;
            text-decoration: none;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            border-color: var(--gray-300);
            color: var(--gray-700);
        }

        .btn-outline:hover {
            background-color: var(--gray-50);
            border-color: var(--gray-400);
        }

        .btn-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        /* Form Styles */
        .form-section {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            margin-bottom: 24px;
            border: 1px solid var(--gray-200);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-row-4 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--gray-700);
        }

        .label-icon {
            margin-right: 6px;
            font-size: 16px;
            color: var(--primary);
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="date"],
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 120px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            transition: var(--transition);
            cursor: pointer;
        }

        .checkbox-item:hover {
            background-color: var(--gray-50);
            border-color: var(--primary);
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }

        table {
            width: 90%; /* Reduced table width */
            border-collapse: collapse;
            margin: 0 auto 20px; /* Center the table */
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .compact-table {
            width: 95%;
            font-size: 12px;
        }

        .compact-table th,
        .compact-table td {
            padding: 8px 10px;
            font-size: 12px;
        }

        .compact-table .table-actions {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .compact-table .btn-table {
            padding: 4px 8px;
            font-size: 11px;
        }

        th,
        td {
            padding: 10px; /* Reduced padding */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size */
        }

        th {
            background-color: var(--gray-50);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
        }

        td input {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            padding: 8px 10px; /* Reduced padding */
            font-size: 14px;
            width: 100%;
        }

        /* Profile Styles */
        .profile-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            padding: 32px;
            border-radius: var(--radius-xl);
            margin-bottom: 32px;
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-full);
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 28px;
            flex-shrink: 0;
            box-shadow: var(--shadow-md);
            border: 3px solid rgba(255,255,255,0.2);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .profile-username {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 12px;
        }

        .profile-role {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: var(--radius-full);
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .profile-form {
            background-color: white;
            padding: 32px;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .form-section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }


        /* Readonly fields */
        input[readonly] {
            background-color: var(--gray-50);
            color: var(--gray-600);
            cursor: not-allowed;
        }

        /* Success/Error Messages */
        .message {
            padding: 16px 20px;
            border-radius: var(--radius-md);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .message-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .message-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Section Headers */
        .section-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--gray-900);
        }

        .section-icon {
            font-size: 24px;
            color: var(--primary);
        }

        /* Required field indicator */
        .required {
            color: var(--danger);
            margin-left: 4px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --sidebar-width: 240px;
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1100;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .topbar {
                padding: 0 16px;
            }

            .content-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .content-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .form-row,
            .form-row-3,
            .form-row-4 {
                grid-template-columns: 1fr;
            }
            
            /* Dashboard Responsive */
            .stats-container {
                grid-template-columns: 1fr 1fr;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
                max-width: 100%;
            }
        }
        
        @media (max-width: 767px) {
            .stats-container {
                grid-template-columns: 1fr;
            }

            .profile-info {
                flex-direction: column;
                text-align: center;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .topbar-actions {
                gap: 8px;
            }

            .action-btn-lg {
                width: 36px;
                height: 36px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .form-section,
            .profile-form {
                padding: 20px;
            }

            .profile-header {
                padding: 24px;
            }
        }

        /* Animation */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .form-section,
        .profile-header,
        .profile-form {
            animation: fadeIn 0.5s ease-out;
        }

        /* Dashboard Styles */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stats-card {
            background-color: white;
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: 16px;
            transition: var(--transition);
        }
        
        .stats-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .stats-content {
            flex: 1;
        }
        
        .stats-title {
            font-size: 14px;
            color: var(--gray-500);
            margin-bottom: 4px;
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 4px;
        }
        
        .stats-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .increase {
            color: var(--success);
        }
        
        .decrease {
            color: var(--danger);
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .dashboard-card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
        }
        
        .chart-container {
            padding: 16px;
            height: 250px;
            position: relative;
        }
        
        .activity-list {
            padding: 16px;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 12px 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }
        
        .activity-title {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .activity-desc {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: var(--gray-500);
        }

        /* Quick Actions Styles */
        .quick-actions {
            padding: 16px;
        }
        
        .quick-action-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 12px;
        }
        
        .quick-action-item:hover {
            background-color: var(--gray-50);
            transform: translateY(-1px);
        }
        
        .quick-action-item:last-child {
            margin-bottom: 0;
        }
        
        .quick-action-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }
        
        .quick-action-content {
            flex: 1;
        }
        
        .quick-action-title {
            font-weight: 500;
            margin-bottom: 4px;
            color: var(--gray-800);
        }
        
        .quick-action-count {
            font-size: 14px;
            color: var(--gray-600);
        }

        /* Hidden sections */
        .hidden {
            display: none;
        }

        /* Loading states */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        /* Enhanced Table Styles */
        table {
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            width: 90%; /* Further reduced table width */
            margin: 0 auto 20px; /* Center the table */
        }

        th, td {
            padding: 10px; /* Reduced padding */
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: 13px; /* Reduced font size */
        }

        th {
            background-color: var(--gray-100);
            font-weight: 600;
            color: var(--gray-700);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Enhanced Button Styles */
        .btn-table {
            padding: 6px 10px; /* Reduced padding */
            border-radius: var(--radius-sm);
            font-size: 12px; /* Reduced font size */
            font-weight: 500;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 4px; /* Reduced gap */
        }

        .btn-table.btn-view {
            background-color: var(--primary);
            color: white;
        }

        .btn-table.btn-view:hover {
            background-color: var(--primary-dark);
        }

        .btn-table.btn-edit {
            background-color: var(--warning);
            color: var(--gray-900);
        }

        .btn-table.btn-edit:hover {
            background-color: var(--warning);
            opacity: 0.8;
        }

        .btn-table.btn-delete {
            background-color: var(--success);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .btn-table.btn-delete:hover {
            background-color: var(--success);
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
        }

        .btn-table.btn-delete:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
        }

        .table-actions {
            display: flex;
            gap: 6px; /* Reduced gap */
            justify-content: center; /* Center the buttons */
        }

        /* Search Bar Styles */
        .search-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-bar input[type="text"] {
            padding: 10px 14px; /* Reduced padding */
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            width: 250px; /* Reduced width */
            transition: var(--transition);
        }

        .search-bar input[type="text"]:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .search-bar button {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 18px; /* Reduced padding */
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-bar button:hover {
            background-color: var(--primary-dark);
        }

        /* Popup Styles */
        .popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Position editUserPopup to the right */
        #editUserPopup.popup {
            justify-content: flex-end;
            padding-right: 20px;
        }

        .popup.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-inner {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 24px;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .modal-title i {
            font-size: 28px;
            color: var(--primary-light);
        }

        .modal-body {
            padding: 32px;
        }

        .permit-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .info-card {
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            padding: 20px;
            border-left: 4px solid var(--primary);
            transition: var(--transition);
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .info-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .info-card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--gray-200);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: var(--gray-600);
            font-size: 14px;
        }

        .info-value {
            font-weight: 600;
            color: var(--gray-800);
            font-size: 14px;
            text-align: right;
        }

        .status-display {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 12px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 600;
        }

        .status-display.active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        /* Badge Styles */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .badge-info {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--info);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease-out;
        }

        .modal-overlay.active {
            display: flex;
        }

        .permit-modal {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease-out;
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: var(--transition);
            z-index: 2;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 24px;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            position: relative;
            overflow: hidden;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .modal-title i {
            font-size: 28px;
            color: var(--primary-light);
        }

        .modal-body {
            padding: 32px;
        }

        .popup-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: var(--transition);
            z-index: 2;
        }

        .popup-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .popup-title {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 24px;
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
            position: relative;
            overflow: hidden;
            font-size: 24px;
            font-weight: 700;
            margin: 0 0 0 0;
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1;
        }

        .popup-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .popup-title i {
            font-size: 28px;
            color: var(--primary-light);
            position: relative;
            z-index: 1;
        }

        .popup-content {
            padding: 32px;
        }

        .popup-content p {
            margin-bottom: 6px; /* Reduced margin */
            font-size: 14px; /* Reduced font size */
        }

        .popup-form .form-group {
            margin-bottom: 16px; /* Reduced margin */
        }

        .popup-form label {
            display: block;
            font-size: 13px; /* Reduced font size */
            font-weight: 600;
            margin-bottom: 6px; /* Reduced margin */
            color: var(--gray-700);
        }

        .popup-form input[type="text"],
        .popup-form input[type="email"],
        .popup-form input[type="password"],
        .popup-form select {
            width: 100%;
            padding: 10px 14px; /* Reduced padding */
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            font-size: 13px; /* Reduced font size */
            background-color: white;
            transition: var(--transition);
            font-family: inherit;
        }

        .popup-form input:focus,
        .popup-form select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .popup-form .btn-primary {
            margin-top: 16px; /* Reduced margin */
        }

        /* Enhanced Edit User Popup Styles */
        #editUserPopup .popup-inner {
            width: 700px; /* Increased width for more space */
            max-height: 80vh; /* Increased max height */
            overflow-y: auto;
            position: relative;
            padding: 32px; /* Increased padding */
            border-top-right-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
            border-top-left-radius: var(--radius-sm);
            border-bottom-left-radius: var(--radius-sm);
            box-shadow: var(--shadow-xl);
        }

        #editUserPopup .popup-form {
            max-height: none; /* Remove max height */
            overflow-y: visible; /* Disable scrolling */
            padding: 24px; /* Increased padding */
        }

        #editUserPopup .popup-form .form-group {
            margin-bottom: 20px; /* Increased spacing */
        }

        #editUserPopup .popup-form label {
            font-size: 14px; /* Slightly larger label font */
            margin-bottom: 8px;
        }

        #editUserPopup .popup-form input[type="text"],
        #editUserPopup .popup-form input[type="email"],
        #editUserPopup .popup-form input[type="password"],
        #editUserPopup .popup-form select {
            padding: 12px 16px; /* Slightly larger input padding */
            font-size: 14px;
        }

        #editUserPopup .popup-form .btn-primary {
            margin-top: 32px; /* Increased button margin */
            padding: 12px 20px; /* Larger button padding */
            font-size: 14px;
        }

        /* Apply form-row to the edit form */
        #editUserPopup .popup-form .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        /* Responsive adjustments for Edit User Popup */
        @media (max-width: 768px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 24px;
            }
        }

        @media (max-width: 576px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 16px;
            }
        }

        /* Badge Styles */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: var(--radius-full);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .badge-info {
            background-color: rgba(6, 182, 212, 0.1);
            color: var(--info);
            border: 1px solid rgba(6, 182, 212, 0.2);
        }

        .badge-secondary {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--gray-600);
            border: 1px solid rgba(107, 114, 128, 0.2);
        }

        /* Enhanced Edit User Popup Styles */
        #editUserPopup .popup-inner {
            width: 800px; /* Further increased width */
            max-height: 90vh; /* Further increased max height */
            padding: 40px; /* Even more padding */
        }

        #editUserPopup .popup-title {
            font-size: 24px; /* Larger title */
            margin-bottom: 24px;
            color: var(--secondary); /* A different color for emphasis */
            text-align: center; /* Center the title */
        }

        #editUserPopup .popup-form {
            padding: 32px; /* More padding inside the form */
            border: 1px solid var(--gray-200); /* Add a subtle border */
            border-radius: var(--radius-lg); /* Rounded corners for the form */
            box-shadow: var(--shadow-sm); /* Subtle shadow for the form */
        }

        #editUserPopup .popup-form .form-group {
            margin-bottom: 24px; /* Increased spacing between form groups */
        }

        #editUserPopup .popup-form label {
            font-size: 15px; /* Slightly larger label font */
            font-weight: 500; /* Slightly lighter font weight */
            color: var(--gray-700);
            margin-bottom: 10px; /* More space below the label */
            display: flex; /* Align label and icon */
            align-items: center;
            gap: 6px; /* Space between label and icon */
        }

        #editUserPopup .popup-form label i {
            color: var(--primary); /* Icon color */
            font-size: 18px; /* Larger icon size */
        }

        #editUserPopup .popup-form input[type="text"],
        #editUserPopup .popup-form input[type="email"],
        #editUserPopup .popup-form input[type="password"],
        #editUserPopup .popup-form select {
            padding: 14px 18px; /* Slightly larger input padding */
            font-size: 15px; /* Slightly larger font size */
            border-radius: var(--radius-md); /* Rounded corners for inputs */
            border: 1px solid var(--gray-300); /* Subtle border */
            transition: border-color 0.3s ease; /* Smooth border color transition */
        }

        #editUserPopup .popup-form input[type="text"]:focus,
        #editUserPopup .popup-form input[type="email"]:focus,
        #editUserPopup .popup-form input[type="password"]:focus,
        #editUserPopup .popup-form select:focus {
            border-color: var(--primary); /* Highlight border on focus */
            box-shadow: none; /* Remove default box-shadow */
        }

        #editUserPopup .popup-form select {
            appearance: none; /* Remove default arrow */
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236B7280'%3E%3Cpath d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E"); /* Custom arrow */
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }

        #editUserPopup .popup-form .btn-primary {
            margin-top: 40px; /* Even more button margin */
            padding: 14px 24px; /* Larger button padding */
            font-size: 16px; /* Larger button font */
            border-radius: var(--radius-lg); /* Rounded corners for the button */
            box-shadow: var(--shadow-sm); /* Subtle shadow for the button */
            transition: background-color 0.3s ease, transform 0.3s ease; /* Smooth transitions */
        }

        #editUserPopup .popup-form .btn-primary:hover {
            background-color: var(--primary-dark); /* Darker background on hover */
            transform: translateY(-2px); /* Slight lift on hover */
            box-shadow: var(--shadow-md); /* Slightly larger shadow on hover */
        }

        #editUserPopup .popup-form .form-row {
            gap: 24px; /* Increased gap between form elements */
        }

        /* Style the close button */
        #editUserPopup .popup-close {
            top: 16px; /* More space from the top */
            right: 16px; /* More space from the right */
            font-size: 24px; /* Larger close button */
            color: var(--gray-500); /* Gray color */
            transition: color 0.3s ease; /* Smooth color transition */
        }

        #editUserPopup .popup-close:hover {
            color: var(--danger); /* Red color on hover */
        }

        /* Enhanced Popup Styles */
        .popup {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }

        .popup.active {
            opacity: 1;
            visibility: visible;
        }

        .popup-inner {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            transform: scale(0.9) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .popup.active .popup-inner {
            transform: scale(1) translateY(0);
        }

        .popup-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 24px;
            padding: 32px 32px 0;
            border-bottom: 2px solid var(--gray-100);
            padding-bottom: 16px;
        }

        .popup-content {
            padding: 24px 32px 32px;
        }

        .popup-close {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            background: var(--gray-100);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--gray-600);
            font-size: 20px;
            z-index: 1;
        }

        .popup-close:hover {
            background: var(--danger);
            color: white;
            transform: scale(1.1);
        }

        .popup-form .form-group {
            margin-bottom: 20px;
        }

        .popup-form .form-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .popup-form .form-group input,
        .popup-form .form-group textarea,
        .popup-form .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-md);
            font-size: 14px;
            transition: all 0.2s ease;
            background: var(--gray-50);
        }

        /* Enhanced styling for date and datetime fields in popup */
        .popup-form .form-group input[type="date"],
        .popup-form .form-group input[type="datetime-local"] {
            background: linear-gradient(135deg, var(--gray-50), white);
            border: 2px solid var(--primary-light);
            color: var(--gray-800);
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        .popup-form .form-group input[type="date"]:focus,
        .popup-form .form-group input[type="datetime-local"]:focus {
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 8px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        /* Special styling for declaration and confirmation date fields */
        #permisDateDeclaration,
        #permisDateConfirmation {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.1));
            border-color: var(--success);
            color: var(--gray-800);
            font-weight: 600;
        }

        #permisDateDeclaration:focus,
        #permisDateConfirmation:focus {
            border-color: var(--success);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.15), 0 4px 8px rgba(16, 185, 129, 0.1);
        }

        /* Enhanced styling for "Déclaré par" and "Confirmé par" fields */
        #permisDeclarePar,
        #permisConfirmePar {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.1));
            border-color: var(--primary);
            color: var(--gray-800);
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }

        #permisDeclarePar:focus,
        #permisConfirmePar:focus {
            border-color: var(--primary-dark);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15), 0 4px 8px rgba(59, 130, 246, 0.1);
        }

        /* Enhanced labels for important fields */
        .popup-form .form-group label[for="permisDeclarePar"],
        .popup-form .form-group label[for="permisConfirmePar"],
        .popup-form .form-group label[for="permisDateDeclaration"],
        .popup-form .form-group label[for="permisDateConfirmation"] {
            color: var(--primary-dark);
            font-weight: 700;
            font-size: 15px;
        }

        .popup-form .form-group label[for="permisDeclarePar"] i,
        .popup-form .form-group label[for="permisConfirmePar"] i,
        .popup-form .form-group label[for="permisDateDeclaration"] i,
        .popup-form .form-group label[for="permisDateConfirmation"] i {
            color: var(--primary);
            font-size: 18px;
        }

        .popup-form .form-group input:focus,
        .popup-form .form-group textarea:focus,
        .popup-form .form-group select:focus {
            outline: none;
            border-color: var(--primary);
            background: white;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .popup-form .form-group input[readonly],
        .popup-form .form-group textarea[readonly] {
            background: var(--gray-100);
            color: var(--gray-700);
            cursor: not-allowed;
        }

        .popup-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .popup-form .form-row {
                grid-template-columns: 1fr;
            }
            
            .popup-inner {
                width: 95%;
                margin: 20px;
            }
            
            .popup-title {
                font-size: 20px;
                padding: 24px 24px 0;
            }
            
            .popup-content {
                padding: 20px 24px 24px;
            }
        }

        /* Compact Table Styles */
        .compact-table {
            font-size: 12px;
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .compact-table th {
            background: var(--gray-50);
            padding: 8px 12px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            border-bottom: 2px solid var(--gray-200);
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .compact-table td {
            padding: 8px 12px;
            border-bottom: 1px solid var(--gray-100);
            color: var(--gray-600);
            vertical-align: middle;
        }

        .compact-table tr:hover {
            background-color: var(--gray-50);
        }

        .compact-table .table-actions {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .compact-table .btn-table {
            padding: 4px 8px;
            font-size: 10px;
            border-radius: var(--radius-sm);
            min-width: auto;
            white-space: nowrap;
        }

        .compact-table .btn-table i {
            font-size: 12px;
            margin-right: 4px;
        }

        .compact-table .badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: var(--radius-sm);
        }

        /* Responsive adjustments for Edit User Popup */
        @media (max-width: 768px) {
            #editUserPopup .popup-inner {
                width: 95%; /* Take up more width on smaller screens */
                padding: 32px;
            }

            #editUserPopup .popup-form .form-row {
                grid-template-columns: 1fr; /* Stack form elements on smaller screens */
            }
        }

        @media (max-width: 576px) {
            #editUserPopup .popup-inner {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon">
                    <img src="sonatrach-logo.png" alt="Logo Sonatrach" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"/>
                </div>
                <span class="logo-text">PT Pro</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class='bx bx-chevron-left'></i>
            </button>
        </div>
        <nav class="sidebar-menu">
            <div class="menu-group">
                <h3 class="menu-title">Navigation</h3>
                <ul class="menu-items">
                    <li class="menu-item">
                        <div class="menu-link active" onclick="showSection('dashboard')">
                            <i class='bx bxs-dashboard menu-icon'></i>
                            <span class="menu-text">Tableau de bord</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('demandes-permis')">
                            <i class='bx bx-list-ul menu-icon'></i>
                            <span class="menu-text">Demandes de permis</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('permis-travail')">
                            <i class='bx bx-file menu-icon'></i>
                            <span class="menu-text">Permis de travail</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('permis-suspendus')">
                            <i class='bx bx-block menu-icon'></i>
                            <span class="menu-text">Permis suspendus/annulés</span>
                        </div>
                    </li>
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('prolongation-permis')">
                            <i class='bx bx-time-five menu-icon'></i>
                            <span class="menu-text">Prolongation du permis</span>
                        </div>
                    </li>

                </ul>
            </div>
            
            <!-- Compte Section -->
            <div class="menu-group">
                <div class="menu-title">Compte</div>
                <ul class="menu-list">
                    <li class="menu-item">
                        <div class="menu-link" onclick="showSection('profile')">
                            <i class='bx bxs-user menu-icon'></i>
                            <span class="menu-text">Mon Profil</span>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>
        <div class="sidebar-footer">
            <div class="user-profile">
                <div class="user-avatar">
                    CU
                    <span class="notification-badge">3</span>
                </div>
                <div class="user-info">
                    <div class="user-name">Coordinateur User</div>
                    <div class="user-role">Coordinateur</div>
                </div>
                <div class="user-actions">
                    <button class="action-btn" onclick="logout()">
                        <i class='bx bx-log-out'></i>
                    </button>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Top Navigation Bar -->
        <div class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class='bx bx-menu'></i>
                </button>
                <div class="breadcrumbs">
                    <span class="breadcrumb-item">ACCUEIL</span>
                    <span class="breadcrumb-item active" id="page-title">TABLEAU DE BORD</span>
                </div>
            </div>
            <div class="topbar-actions">
                <div class="topbar-action">
                    <button class="action-btn-lg">
                        <i class='bx bx-bell'></i>
                        <span class="notification-badge">1</span>
                    </button>
                </div>
                <div class="topbar-action" id="userDropdownTrigger">
                    <button class="action-btn-lg">
                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px;">
                            CU
                        </div>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <div class="dropdown-header">
                            <div class="dropdown-title">Paramètres du compte</div>
                        </div>
                        <div class="dropdown-item" onclick="showSection('profile')">
                            <i class='bx bx-user'></i>
                            <span>Mon profil</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item" onclick="logout()">
                            <i class='bx bx-log-out'></i>
                            <span>Déconnexion</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-wrapper">
            <!-- Dashboard Section -->
            <section id="dashboard" class="form-section">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Tableau de bord</h1>
                        <p class="content-subtitle">Aperçu du système de gestion des permis de travail</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshDashboard()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="stats-container">
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(37, 99, 235, 0.1); color: var(--primary);">
                            <i class='bx bx-list-ul'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Demandes en attente</div>
                            <div class="stats-value">6</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>2 nouvelles aujourd'hui</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(16, 185, 129, 0.1); color: var(--success);">
                            <i class='bx bx-file-plus'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Permis émis</div>
                            <div class="stats-value">24</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>8 cette semaine</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                            <i class='bx bx-check-circle'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Prêts pour clôture</div>
                            <div class="stats-value">4</div>
                            <div class="stats-change decrease">
                                <i class='bx bx-down-arrow-alt'></i>
                                <span>En attente de traitement</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <div class="stats-icon" style="background-color: rgba(139, 69, 19, 0.1); color: #8B4513;">
                            <i class='bx bx-time-five'></i>
                        </div>
                        <div class="stats-content">
                            <div class="stats-title">Prolongations approuvées</div>
                            <div class="stats-value">3</div>
                            <div class="stats-change increase">
                                <i class='bx bx-up-arrow-alt'></i>
                                <span>À émettre</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3 class="card-title">Actions à effectuer</h3>
                        </div>
                        <div class="quick-actions">
                            <div class="quick-action-item" onclick="showSection('demandes-permis')">
                                <div class="quick-action-icon" style="background-color: rgba(37, 99, 235, 0.1); color: var(--primary);">
                                    <i class='bx bx-list-ul'></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">Vérifier demandes</div>
                                    <div class="quick-action-count">6 en attente</div>
                                </div>
                            </div>
                            
                            <div class="quick-action-item" onclick="showSection('prolongation-permis')">
                                <div class="quick-action-icon" style="background-color: rgba(139, 69, 19, 0.1); color: #8B4513;">
                                    <i class='bx bx-time-five'></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">Émettre prolongations</div>
                                    <div class="quick-action-count">3 approuvées</div>
                                </div>
                            </div>
                            
                            <div class="quick-action-item" onclick="showSection('permis-travail')">
                                <div class="quick-action-icon" style="background-color: rgba(245, 158, 11, 0.1); color: var(--warning);">
                                    <i class='bx bx-check-circle'></i>
                                </div>
                                <div class="quick-action-content">
                                    <div class="quick-action-title">Clôturer permis</div>
                                    <div class="quick-action-count">4 prêts</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Demandes de Permis Section -->
            <section id="demandes-permis" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Demandes de Permis</h1>
                        <p class="content-subtitle">Consultez toutes les demandes de permis de travail</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshDemandes()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchDemandes" placeholder="Rechercher une demande..." onkeyup="searchDemandes()">
                    <button onclick="searchDemandes()">Rechercher</button>
                </div>

                <table class="compact-table">
                    <thead>
                        <tr>
                            <th>ID Demande</th>
                            <th>Demandeur</th>
                            <th>Type de travail</th>
                            <th>Zone</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="demandesTableBody">
                        <tr data-id="PT-2024-001">
                            <td>PT-2024-001</td>
                            <td>Ahmed Benali</td>
                            <td>Travaux dont les dangers associés sont potentiellement dangereux</td>
                            <td>Zone A - Production</td>
                            <td>15/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-001')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-001')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-001')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-002">
                            <td>PT-2024-002</td>
                            <td>Fatima Zohra</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>16/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-002')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-002')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-002')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-003">
                            <td>PT-2024-003</td>
                            <td>Mohamed Larbi</td>
                            <td>Travaux d'urgence</td>
                            <td>Zone C - Laboratoire</td>
                            <td>17/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-003')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-003')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-003')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-004">
                            <td>PT-2024-004</td>
                            <td>Karim Messaoud</td>
                            <td>Travaux dispensés du permis de travail</td>
                            <td>Zone D - Utilités</td>
                            <td>18/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-004')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-004')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-004')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-005">
                            <td>PT-2024-005</td>
                            <td>Yacine Boumediene</td>
                            <td>Travaux dont les dangers associés sont potentiellement dangereux</td>
                            <td>Zone A - Production</td>
                            <td>19/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-005')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-005')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-005')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-006">
                            <td>PT-2024-006</td>
                            <td>Samira Khelifi</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>20/01/2024</td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="verifierConformite('PT-2024-006')">
                                        <i class='bx bx-check-shield'></i>
                                        Vérifier la conformité
                                    </button>
                                    <button class="btn-table btn-delete" onclick="rejeterDemande('PT-2024-006')">
                                        <i class='bx bx-x'></i>
                                        Rejeter
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettrePermis('PT-2024-006')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Permis de travail Section -->
            <section id="permis-travail" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis de travail</h1>
                        <p class="content-subtitle">Consultez tous les permis de travail terminés</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermis()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchPermis" placeholder="Rechercher un permis..." onkeyup="searchPermis()">
                    <button onclick="searchPermis()">Rechercher</button>
                </div>

                <table class="compact-table">
                    <thead>
                        <tr>
                            <th>ID Permis</th>
                            <th>Demandeur</th>
                            <th>Type de travail</th>
                            <th>Zone</th>
                            <th>Date début</th>
                            <th>Date fin</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="permisTableBody">
                        <tr data-id="PT-2024-001">
                            <td>PT-2024-001</td>
                            <td>Ahmed Benali</td>
                            <td>Travaux dont les dangers associés sont potentiellement dangereux</td>
                            <td>Zone A - Production</td>
                            <td>20/01/2024</td>
                            <td>22/01/2024</td>
                            <td><span class="badge badge-warning">Prêt pour clôture</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermis('PT-2024-001')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermis('PT-2024-001')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-003">
                            <td>PT-2024-003</td>
                            <td>Karim Messaoud</td>
                            <td>Travaux d'urgence</td>
                            <td>Zone C - Laboratoire</td>
                            <td>18/01/2024</td>
                            <td>21/01/2024</td>
                            <td><span class="badge badge-warning">Prêt pour clôture</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermis('PT-2024-003')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermis('PT-2024-003')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-005">
                            <td>PT-2024-005</td>
                            <td>Samira Khelifi</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>19/01/2024</td>
                            <td>20/01/2024</td>
                            <td><span class="badge badge-warning">Prêt pour clôture</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermis('PT-2024-005')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermis('PT-2024-005')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-007">
                            <td>PT-2024-007</td>
                            <td>Fatima Zohra</td>
                            <td>Travaux dispensés du permis de travail</td>
                            <td>Zone D - Utilités</td>
                            <td>17/01/2024</td>
                            <td>18/01/2024</td>
                            <td><span class="badge badge-warning">Prêt pour clôture</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermis('PT-2024-007')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermis('PT-2024-007')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Vérification Conformité Section -->
            <section id="verification-conformite" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Vérification de Conformité</h1>
                        <p class="content-subtitle">Vérifiez la conformité des demandes et ajoutez des remarques</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-check-shield section-icon'></i>
                        <h2 class="section-title">Demande en cours de vérification</h2>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>ID Demande</label>
                            <input type="text" value="PT-2024-001" readonly>
                        </div>
                        <div class="form-group">
                            <label>Initiateur</label>
                            <input type="text" value="Ahmed Benali" readonly>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Type de travail</label>
                            <input type="text" value="Travaux à chaud" readonly>
                        </div>
                        <div class="form-group">
                            <label>Zone</label>
                            <input type="text" value="Zone A - Production" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Description des travaux</label>
                        <textarea readonly>Soudage de conduites de vapeur dans l'unité de production. Travaux prévus sur 3 jours avec équipe de 4 personnes.</textarea>
                    </div>

                    <div class="form-group">
                        <label>Statut de conformité <span class="required">*</span></label>
                        <select id="statutConformite" onchange="toggleRemarques()">
                            <option value="">Sélectionner le statut</option>
                            <option value="conforme">Conforme</option>
                            <option value="non-conforme">Non conforme</option>
                        </select>
                    </div>

                    <div class="form-group" id="remarquesSection" style="display: none;">
                        <label>Remarques de non-conformité <span class="required">*</span></label>
                        <textarea id="remarques" placeholder="Détaillez les points de non-conformité et les corrections nécessaires..."></textarea>
                    </div>

                    <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                        <button type="button" class="btn btn-outline" onclick="annulerVerification()">Annuler</button>
                        <button type="button" class="btn btn-primary" onclick="validerConformite()">
                            <i class='bx bx-check btn-icon'></i>
                            Valider la vérification
                        </button>
                    </div>
                </div>
            </section>

            <!-- Émission Permis Section -->
            <section id="emission-permis" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Émission de Permis</h1>
                        <p class="content-subtitle">Émettez un nouveau permis de travail</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-file-plus section-icon'></i>
                        <h2 class="section-title">Nouveau Permis de Travail</h2>
                    </div>

                    <form id="emissionPermisForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>ID Demande de référence <span class="required">*</span></label>
                                <select id="demandeReference" required>
                                    <option value="">Sélectionner une demande</option>
                                    <option value="PT-2024-001">PT-2024-001 - Ahmed Benali</option>
                                    <option value="PT-2024-004">PT-2024-004 - Karim Messaoud</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>ID Permis <span class="required">*</span></label>
                                <input type="text" id="numeroPermis" value="PT-2024-005" readonly>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Date de début <span class="required">*</span></label>
                                <input type="date" id="dateDebut" required>
                            </div>
                            <div class="form-group">
                                <label>Date de fin <span class="required">*</span></label>
                                <input type="date" id="dateFin" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Conditions particulières</label>
                            <textarea id="conditionsParticulieres" placeholder="Spécifiez les conditions particulières d'exécution..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>Mesures de sécurité obligatoires</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="epi" name="mesures" value="epi">
                                    <label for="epi">Port d'EPI obligatoire</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="consignation" name="mesures" value="consignation">
                                    <label for="consignation">Consignation électrique</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="surveillance" name="mesures" value="surveillance">
                                    <label for="surveillance">Surveillance permanente</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="ventilation" name="mesures" value="ventilation">
                                    <label for="ventilation">Ventilation forcée</label>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                            <button type="button" class="btn btn-outline" onclick="annulerEmission()">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class='bx bx-file-plus btn-icon'></i>
                                Émettre le permis
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Permis suspendus/annulés Section -->
            <section id="permis-suspendus" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Permis suspendus/annulés</h1>
                        <p class="content-subtitle">Permis suspendus ou annulés par le responsable HSE ou le représentant de l'autorité de zone</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshPermisSuspendus()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchPermisSuspendus" placeholder="Rechercher un permis suspendu..." onkeyup="searchPermisSuspendus()">
                    <button onclick="searchPermisSuspendus()">Rechercher</button>
                </div>

                <table class="compact-table">
                    <thead>
                        <tr>
                            <th>ID Permis</th>
                            <th>Demandeur</th>
                            <th>Type de travail</th>
                            <th>Zone</th>
                            <th>Date suspension</th>
                            <th>Suspendu par</th>
                            <th>Motif</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="permisSuspendusTableBody">
                        <tr data-id="PT-2024-008">
                            <td>PT-2024-008</td>
                            <td>Mohamed Larbi</td>
                            <td>Travaux électriques</td>
                            <td>Zone A - Production</td>
                            <td>23/01/2024</td>
                            <td>Marie Sécurité (Responsable HSE)</td>
                            <td>Non-respect des consignes de sécurité</td>
                            <td><span class="badge badge-danger">Suspendu</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermisSuspendu('PT-2024-008')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermisSuspendu('PT-2024-008')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-009">
                            <td>PT-2024-009</td>
                            <td>Aicha Benali</td>
                            <td>Travaux de maintenance</td>
                            <td>Zone B - Stockage</td>
                            <td>24/01/2024</td>
                            <td>Jean Autorité (Représentant autorité zone)</td>
                            <td>Conditions météorologiques dangereuses</td>
                            <td><span class="badge badge-warning">Annulé</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermisSuspendu('PT-2024-009')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermisSuspendu('PT-2024-009')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-010">
                            <td>PT-2024-010</td>
                            <td>Rachid Khelifi</td>
                            <td>Travaux en hauteur</td>
                            <td>Zone C - Laboratoire</td>
                            <td>25/01/2024</td>
                            <td>Pierre Représentant (Représentant HSE)</td>
                            <td>Équipement de protection défaillant</td>
                            <td><span class="badge badge-danger">Suspendu</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirPermisSuspendu('PT-2024-010')">
                                        <i class='bx bx-show'></i>
                                        Voir
                                    </button>
                                    <button class="btn-table btn-delete" onclick="cloturerPermisSuspendu('PT-2024-010')">
                                        <i class='bx bx-check-circle'></i>
                                        Clôturer
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Prolongation du permis Section -->
            <section id="prolongation-permis" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Prolongation du permis</h1>
                        <p class="content-subtitle">Demandes de prolongation approuvées par le représentant de l'autorité de zone</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-outline" onclick="refreshProlongations()">
                            <i class='bx bx-refresh btn-icon'></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-bar">
                    <input type="text" id="searchProlongations" placeholder="Rechercher une demande de prolongation..." onkeyup="searchProlongations()">
                    <button onclick="searchProlongations()">Rechercher</button>
                </div>

                <table class="compact-table">
                    <thead>
                        <tr>
                            <th>ID Permis</th>
                            <th>Demandeur</th>
                            <th>Type de travail</th>
                            <th>Zone</th>
                            <th>Date fin actuelle</th>
                            <th>Nouvelle date fin</th>
                            <th>Durée</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="prolongationsTableBody">
                        <tr data-id="PT-2024-001">
                            <td>PT-2024-001</td>
                            <td>Ahmed Benali</td>
                            <td>Travaux dont les dangers associés sont potentiellement dangereux</td>
                            <td>Zone A - Production</td>
                            <td>22/01/2024</td>
                            <td>25/01/2024</td>
                            <td>3 jours</td>
                            <td><span class="badge badge-success">Approuvée par autorité zone</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-001')">
                                        <i class='bx bx-show'></i>
                                        Voir la demande
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettreProlongation('PT-2024-001')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre prolongation
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-003">
                            <td>PT-2024-003</td>
                            <td>Karim Messaoud</td>
                            <td>Travaux d'urgence</td>
                            <td>Zone C - Laboratoire</td>
                            <td>21/01/2024</td>
                            <td>23/01/2024</td>
                            <td>2 jours</td>
                            <td><span class="badge badge-success">Approuvée par autorité zone</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-003')">
                                        <i class='bx bx-show'></i>
                                        Voir la demande
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettreProlongation('PT-2024-003')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre prolongation
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr data-id="PT-2024-005">
                            <td>PT-2024-005</td>
                            <td>Yacine Boumediene</td>
                            <td>Travaux routiniers à faibles risques</td>
                            <td>Zone B - Stockage</td>
                            <td>20/01/2024</td>
                            <td>24/01/2024</td>
                            <td>4 jours</td>
                            <td><span class="badge badge-success">Approuvée par autorité zone</span></td>
                            <td>
                                <div class="table-actions">
                                    <button class="btn-table btn-view" onclick="voirDemandeProlongation('PT-2024-005')">
                                        <i class='bx bx-show'></i>
                                        Voir la demande
                                    </button>
                                    <button class="btn-table btn-view" onclick="emettreProlongation('PT-2024-005')" style="background-color: var(--success);">
                                        <i class='bx bx-file-plus'></i>
                                        Émettre prolongation
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>


            <!-- Clôture Permis Section -->
            <section id="cloture-permis" class="form-section hidden">
                <div class="content-header">
                    <div>
                        <h1 class="content-title">Clôture de Permis</h1>
                        <p class="content-subtitle">Enregistrez la fin des travaux et clôturez les permis</p>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-header">
                        <i class='bx bx-check-circle section-icon'></i>
                        <h2 class="section-title">Clôture de Permis de Travail</h2>
                    </div>

                    <form id="clotureForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>ID Permis à clôturer <span class="required">*</span></label>
                                <select id="permisACloture" required>
                                    <option value="">Sélectionner un permis</option>
                                    <option value="PT-2024-001">PT-2024-001 - Ahmed Benali</option>
                                    <option value="PT-2024-002">PT-2024-002 - Fatima Zohra</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Date de fin effective <span class="required">*</span></label>
                                <input type="date" id="dateFinEffective" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Travaux réalisés conformément au permis</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="radio" id="conforme-oui" name="conformite" value="oui" required>
                                    <label for="conforme-oui">Oui, travaux conformes</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="radio" id="conforme-non" name="conformite" value="non" required>
                                    <label for="conforme-non">Non, écarts constatés</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="ecartsSection" style="display: none;">
                            <label>Description des écarts <span class="required">*</span></label>
                            <textarea id="descriptionEcarts" placeholder="Décrivez les écarts constatés..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>Observations finales</label>
                            <textarea id="observationsFinales" placeholder="Ajoutez vos observations sur la réalisation des travaux..."></textarea>
                        </div>

                        <div class="form-group">
                            <label>Zone remise en état</label>
                            <div class="checkbox-group">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="nettoyage" name="remiseEtat" value="nettoyage">
                                    <label for="nettoyage">Nettoyage effectué</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="evacuation" name="remiseEtat" value="evacuation">
                                    <label for="evacuation">Évacuation des déchets</label>
                                </div>
                                <div class="checkbox-item">
                                    <input type="checkbox" id="remise-service" name="remiseEtat" value="remise-service">
                                    <label for="remise-service">Remise en service</label>
                                </div>
                            </div>
                        </div>

                        <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                            <button type="button" class="btn btn-outline" onclick="annulerCloture()">Annuler</button>
                            <button type="submit" class="btn btn-primary">
                                <i class='bx bx-check-circle btn-icon'></i>
                                Clôturer le permis
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="hidden">
                <!-- Profile Header -->
                <div class="profile-header">
                    <div class="profile-info">
                        <div class="profile-avatar">CU</div>
                        <div class="profile-details">
                            <h1 class="profile-name">Coordinateur User</h1>
                            <div class="profile-username">@coordinateuruser</div>
                            <span class="profile-role">
                                <i class='bx bx-shield'></i>
                                Coordinateur
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="profile-form">
                    <h2 class="form-section-title">
                        <i class='bx bx-user'></i>
                        Informations du Profil
                    </h2>

                    <form id="profileForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom complet
                                </label>
                                <input type="text" value="Coordinateur User" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-user label-icon'></i>
                                    Nom d'utilisateur
                                </label>
                                <input type="text" value="coordinateuruser" readonly/>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-briefcase label-icon'></i>
                                    Fonction
                                </label>
                                <input type="text" value="Coordinateur" readonly/>
                            </div>
                            <div class="form-group">
                                <label>
                                    <i class='bx bx-envelope label-icon'></i>
                                    Email <span class="required">*</span>
                                </label>
                                <input type="email" id="userEmail" value="<EMAIL>" required placeholder="Entrez votre adresse email"/>
                            </div>
                        </div>

                        <!-- Success/Error Message -->
                        <div id="profileMessage" class="hidden"></div>

                        <!-- Submit Button -->
                        <div style="display: flex; justify-content: flex-end; margin-top: 24px;">
                            <button type="submit" class="btn btn-primary" id="profileSubmitBtn">
                                <i class='bx bx-save btn-icon'></i>
                                Mettre à jour l'email
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- View User Popup -->
            <div class="modal-overlay" id="viewUserPopup">
                <div class="permit-modal">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class='bx bx-user'></i>
                            <span>Informations de l'utilisateur</span>
                        </h2>
                        <button class="modal-close" onclick="closePopup('viewUserPopup')">
                            <i class='bx bx-x'></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="permit-info-grid">
                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-info-circle'></i>
                                    </div>
                                    <h3 class="info-card-title">Informations Personnelles</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Nom Complet</span>
                                    <span class="info-value" id="viewFullName">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Nom d'utilisateur</span>
                                    <span class="info-value" id="viewUsername">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Email</span>
                                    <span class="info-value" id="viewEmail">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Fonction</span>
                                    <span class="info-value" id="viewFunction">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Rôle</span>
                                    <span class="info-value" id="viewRole">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit User Popup -->
            <div class="modal-overlay" id="editUserPopup">
                <div class="permit-modal">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class='bx bx-edit'></i>
                            <span>Modifier l'utilisateur</span>
                        </h2>
                        <button class="modal-close" onclick="closePopup('editUserPopup')">
                            <i class='bx bx-x'></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form class="popup-form" id="editUserForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editFullName"><i class='bx bx-user'></i>Nom Complet</label>
                                <input type="text" id="editFullName" name="fullName">
                            </div>
                            <div class="form-group">
                                <label for="editUsername"><i class='bx bx-id-card'></i>Nom d'utilisateur</label>
                                <input type="text" id="editUsername" name="username">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="editEmail"><i class='bx bx-envelope'></i>Email</label>
                                <input type="email" id="editEmail" name="email">
                            </div>
                            <div class="form-group">
                                <label for="editFunction"><i class='bx bx-briefcase'></i>Fonction</label>
                                <input type="text" id="editFunction" name="function">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="editRole"><i class='bx bx-shield'></i>Rôle</label>
                            <select id="editRole" name="role">
                                <option value="Demandeur">Demandeur</option>
                                <option value="coordinateur">Coordinateur</option>
                                <option value="responsable-hse">Responsable HSE</option>
                                <option value="autorite-zone">Autorité de Zone</option>
                                <option value="responsable-execution">Responsable d'Exécution</option>
                                <option value="representant-az">Représentant AZ</option>
                                <option value="representant-hse">Représentant HSE</option>
                            </select>
                        </div>
                        
                        <!-- Zone Selection for Edit User -->
                        <div class="form-group" id="edit-zone-selection" style="display: none;">
                            <label for="editZone"><i class='bx bx-map'></i>Zone de responsabilité</label>
                            <select id="editZone" name="zone">
                                <option value="">Sélectionner une zone</option>
                                <option value="Zone A - Production">Zone A - Production</option>
                                <option value="Zone B - Stockage">Zone B - Stockage</option>
                                <option value="Zone C - Laboratoire">Zone C - Laboratoire</option>
                                <option value="Zone D - Utilities">Zone D - Utilities</option>
                                <option value="Zone E - Administration">Zone E - Administration</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="editPassword"><i class='bx bx-lock'></i>Mot de passe</label>
                            <input type="password" id="editPassword" name="password" placeholder="Laissez vide pour ne pas changer">
                        </div>
                        <button type="submit" class="btn btn-primary" onclick="submitEditForm(event)">Enregistrer</button>
                    </form>
                    </div>
                </div>
            </div>

            <!-- Conformity Verification Popup -->
            <div class="modal-overlay" id="conformityPopup">
                <div class="permit-modal">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class='bx bx-check-shield'></i>
                            <span>Vérification de Conformité</span>
                        </h2>
                        <button class="modal-close" onclick="closePopup('conformityPopup')">
                            <i class='bx bx-x'></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form class="popup-form" id="conformityForm">
                            <div class="form-group">
                                <label><i class='bx bx-file'></i>ID Demande</label>
                                <input type="text" id="conformityDemandeId" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label><i class='bx bx-check-shield'></i>Statut de conformité <span class="required">*</span></label>
                                <select id="conformityStatus" required>
                                    <option value="">Sélectionner le statut</option>
                                    <option value="conforme">Conforme</option>
                                    <option value="non-conforme">Non conforme</option>
                                </select>
                            </div>
                            
                            <div class="form-group" id="conformityRemarksSection" style="display: none;">
                                <label><i class='bx bx-message-detail'></i>Remarques de non-conformité <span class="required">*</span></label>
                                <textarea id="conformityRemarks" placeholder="Détaillez les points de non-conformité et les corrections nécessaires..." rows="4"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class='bx bx-check btn-icon'></i>
                                Valider la vérification
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Prolongation Request Details Popup -->
            <div class="modal-overlay" id="prolongationPopup">
                <div class="permit-modal">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class='bx bx-time-five'></i>
                            <span>Détails de la demande de prolongation</span>
                        </h2>
                        <button class="modal-close" onclick="closePopup('prolongationPopup')">
                            <i class='bx bx-x'></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="permit-info-grid">
                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-info-circle'></i>
                                    </div>
                                    <h3 class="info-card-title">Informations Générales</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">ID Permis</span>
                                    <span class="info-value" id="prolongationPermisId">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Demandeur</span>
                                    <span class="info-value" id="prolongationDemandeur">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Type de travail</span>
                                    <span class="info-value" id="prolongationTypeTravail">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Zone</span>
                                    <span class="info-value" id="prolongationZone">-</span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-calendar'></i>
                                    </div>
                                    <h3 class="info-card-title">Planification</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Durée de prolongation</span>
                                    <span class="info-value" id="prolongationDuree">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date fin actuelle</span>
                                    <span class="info-value" id="prolongationDateFinActuelle">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Nouvelle date fin</span>
                                    <span class="info-value" id="prolongationNouvelleDateFin">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Statut d'approbation</span>
                                    <span class="info-value" id="prolongationStatut">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Approuvé par</span>
                                    <span class="info-value" id="prolongationApprouvePar">-</span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-message-detail'></i>
                                    </div>
                                    <h3 class="info-card-title">Justification</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Motif de prolongation</span>
                                    <span class="info-value" id="prolongationJustification">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Permit Details Popup -->
            <div class="modal-overlay" id="permisPopup">
                <div class="permit-modal">
                    <div class="modal-header">
                        <h2 class="modal-title">
                            <i class='bx bx-file-blank'></i>
                            <span>Détails du permis de travail</span>
                        </h2>
                        <button class="modal-close" onclick="closePopup('permisPopup')">
                            <i class='bx bx-x'></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="permit-info-grid">
                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-info-circle'></i>
                                    </div>
                                    <h3 class="info-card-title">Informations Générales</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">ID Permis</span>
                                    <span class="info-value" id="permisId">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Demandeur</span>
                                    <span class="info-value" id="permisDemandeur">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Type de travail</span>
                                    <span class="info-value" id="permisTypeTravail">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Zone</span>
                                    <span class="info-value" id="permisZone">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Entreprise</span>
                                    <span class="info-value" id="permisEntreprise">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Responsable d'exécution</span>
                                    <span class="info-value" id="permisResponsable">-</span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-calendar'></i>
                                    </div>
                                    <h3 class="info-card-title">Planification</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date début</span>
                                    <span class="info-value" id="permisDateDebut">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date fin</span>
                                    <span class="info-value" id="permisDateFin">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Heure début</span>
                                    <span class="info-value" id="permisHeureDebut">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Heure fin</span>
                                    <span class="info-value" id="permisHeureFin">-</span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-shield-check'></i>
                                    </div>
                                    <h3 class="info-card-title">Sécurité</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Description des travaux</span>
                                    <span class="info-value" id="permisDescription">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Risques identifiés</span>
                                    <span class="info-value" id="permisRisques">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Mesures de sécurité</span>
                                    <span class="info-value" id="permisMesures">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">EPI requis</span>
                                    <span class="info-value" id="permisEPI">-</span>
                                </div>
                            </div>

                            <div class="info-card">
                                <div class="info-card-header">
                                    <div class="info-card-icon">
                                        <i class='bx bx-check-circle'></i>
                                    </div>
                                    <h3 class="info-card-title">Informations de clôture</h3>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Déclaré fin de travail par</span>
                                    <span class="info-value" id="permisDeclarePar">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Confirmé par (Représentant AZ)</span>
                                    <span class="info-value" id="permisConfirmePar">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date de déclaration fin</span>
                                    <span class="info-value" id="permisDateDeclaration">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Date de confirmation</span>
                                    <span class="info-value" id="permisDateConfirmation">-</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Remarques de fin de travail</span>
                                    <span class="info-value" id="permisRemarques">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Toggle sidebar on mobile
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            menuToggle.addEventListener('click', function () {
                sidebar.classList.toggle('active');
            });

            // Toggle sidebar collapse/expand
            sidebarToggle.addEventListener('click', function () {
                sidebar.classList.toggle('collapsed');
                const icon = sidebarToggle.querySelector('i');
                if (sidebar.classList.contains('collapsed')) {
                    icon.classList.remove('bx-chevron-left');
                    icon.classList.add('bx-chevron-right');
                } else {
                    icon.classList.remove('bx-chevron-right');
                    icon.classList.add('bx-chevron-left');
                }
            });

            // User dropdown toggle
            const userDropdownTrigger = document.getElementById('userDropdownTrigger');
            const userDropdown = document.getElementById('userDropdown');

            userDropdownTrigger.addEventListener('click', function (e) {
                e.stopPropagation();
                userDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function (e) {
                if (!userDropdown.contains(e.target) && e.target !== userDropdownTrigger && !userDropdownTrigger.contains(e.target)) {
                    userDropdown.classList.remove('active');
                }
            });

            // Notification button action
            const notificationBtns = document.querySelectorAll('.action-btn-lg .bx-bell');
            notificationBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    alert('Vous avez 1 nouvelle notification');
                });
            });

            // Simulate enterprise-level resize handling
            function handleResize() {
                if (window.innerWidth < 992) {
                    sidebar.classList.remove('collapsed');
                    sidebarToggle.style.display = 'none';
                    menuToggle.style.display = 'flex';
                } else {
                    menuToggle.style.display = 'none';
                    sidebarToggle.style.display = 'flex';
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize();
        });

        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll("section").forEach(sec => sec.classList.add('hidden'));
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            // Update page title
            const titles = {
                'dashboard': 'TABLEAU DE BORD',
                'demandes-permis': 'DEMANDES DE PERMIS',
                'permis-travail': 'PERMIS DE TRAVAIL',
                'prolongation-permis': 'PROLONGATION DU PERMIS',
                'permis-suspendus': 'PERMIS SUSPENDUS/ANNULÉS',
                'profile': 'MON PROFIL'
            };
            document.getElementById("page-title").textContent = titles[sectionId];

            // Update active link in sidebar
            document.querySelectorAll(".menu-link").forEach(link => {
                link.classList.remove("active");
            });

            // Find the clicked element or its parent with class 'menu-link'
            let target = event.target;
            while (target && !target.classList.contains('menu-link')) {
                target = target.parentNode;
            }

            if (target && target.classList.contains('menu-link')) {
                target.classList.add("active");
            }
        }

        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                alert('Déconnexion en cours...');
                // Add your logout logic here
                window.location.href = 'login.html'; // Redirect to login page
            }
        }

        function addUser() {
            showSection('add-user');
        }

        function viewUser(element) {
            const email = element.dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${email}"]`);

            if (row) {
                const fullName = row.dataset.fullName;
                const username = row.dataset.username;
                const fonction = row.dataset.function;
                const role = row.dataset.role;

                document.getElementById('viewFullName').textContent = fullName;
                document.getElementById('viewUsername').textContent = username;
                document.getElementById('viewEmail').textContent = email;
                document.getElementById('viewFunction').textContent = fonction;
                document.getElementById('viewRole').textContent = role;

                openPopup('viewUserPopup');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        function editUser(element) {
            const email = element.dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${email}"]`);

            if (row) {
                const fullName = row.dataset.fullName;
                const username = row.dataset.username;
                const fonction = row.dataset.function;
                const role = row.dataset.role;

                document.getElementById('editFullName').value = fullName;
                document.getElementById('editUsername').value = username;
                document.getElementById('editEmail').value = email;
                document.getElementById('editFunction').value = fonction;
                document.getElementById('editRole').value = role;
                
                // Handle zone selection visibility for Autorité de Zone
                const editZoneSelection = document.getElementById('edit-zone-selection');
                const editZone = document.getElementById('editZone');
                
                if (role === 'autorite-zone') {
                    editZoneSelection.style.display = 'block';
                    // If the user has a zone defined in their data, set it
                    if (row.dataset.zone) {
                        editZone.value = row.dataset.zone;
                    }
                } else {
                    editZoneSelection.style.display = 'none';
                }

                // Store the email in a data attribute of the form for later use
                document.getElementById('editUserForm').dataset.email = email;

                openPopup('editUserPopup');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        function deleteUser(email) {
            if (confirm('Êtes-vous sûr de vouloir supprimer l\'utilisateur avec l\'email : ' + email + ' ?')) {
                // Find the row with the matching email
                const row = document.querySelector(`tr[data-email="${email}"]`);

                if (row) {
                    row.remove(); // Remove the row from the table
                    alert('Utilisateur supprimé avec succès.');
                } else {
                    alert('Utilisateur non trouvé.');
                }
            }
        }

        function searchTable() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchInput");
            filter = input.value.toUpperCase();
            table = document.getElementById("users").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) { // Start from 1 to skip the header row
                tr[i].style.display = "none"; // Hide the row initially
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = ""; // Display the row if a match is found
                            break; // No need to check other columns in the same row
                        }
                    }
                }
            }
        }

        function openPopup(popupId) {
            document.getElementById(popupId).classList.add('active');
        }

        function closePopup(popupId) {
            document.getElementById(popupId).classList.remove('active');
        }

        function submitEditForm(event) {
            event.preventDefault(); // Prevent the default form submission

            // Get the form values
            const fullName = document.getElementById('editFullName').value;
            const username = document.getElementById('editUsername').value;
            const email = document.getElementById('editEmail').value;
            const fonction = document.getElementById('editFunction').value;
            const role = document.getElementById('editRole').value;
            const password = document.getElementById('editPassword').value; // Password can be empty if not changed
            const zone = document.getElementById('editZone').value; // Zone will be used if role is Autorité de Zone

            // Get the email of the user being edited from the form's data attribute
            const userEmail = document.getElementById('editUserForm').dataset.email;

            // Find the row with the matching email
            const row = document.querySelector(`tr[data-email="${userEmail}"]`);

            if (row) {
                // Update the row's data attributes and content
                row.dataset.fullName = fullName;
                row.dataset.username = username;
                row.dataset.function = fonction;
                row.dataset.role = role;
                
                // Store zone information if the role is Autorité de Zone
                if (role === 'autorite-zone') {
                    row.dataset.zone = zone;
                } else {
                    delete row.dataset.zone;
                }

                // Update the table cells with the new values
                const cells = row.getElementsByTagName('td');
                cells[0].textContent = fullName;
                cells[1].textContent = email;
                cells[2].textContent = fonction;
                cells[3].textContent = role;

                // Close the popup after submission
                closePopup('editUserPopup');
                alert('Utilisateur mis à jour avec succès.');
            } else {
                alert('Utilisateur non trouvé.');
            }
        }

        // Handle zone selection visibility based on Autorité de Zone role
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize charts for dashboard
            initCharts();
            
            // For Add User form
            const autoriteCheckbox = document.getElementById('role-autorite');
            const zoneSelection = document.getElementById('zone-selection');
            const zoneSelect = document.getElementById('user-zone');
            
            // Show/hide zone selection based on checkbox state
            autoriteCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    zoneSelection.style.display = 'block';
                    zoneSelect.setAttribute('required', 'required');
                } else {
                    zoneSelection.style.display = 'none';
                    zoneSelect.removeAttribute('required');
                }
            });

            // Validate form submission
            const addUserForm = document.getElementById('addUserForm');
            addUserForm.addEventListener('submit', function(e) {
                if (autoriteCheckbox.checked && zoneSelect.value === '') {
                    e.preventDefault();
                    alert('Veuillez sélectionner une zone de responsabilité pour l\'Autorité de Zone.');
                }
            });
            
            // For Edit User form
            const editRole = document.getElementById('editRole');
            const editZoneSelection = document.getElementById('edit-zone-selection');
            const editZone = document.getElementById('editZone');
            
            // Show/hide zone selection based on role dropdown
            editRole.addEventListener('change', function() {
                if (this.value === 'autorite-zone') {
                    editZoneSelection.style.display = 'block';
                    editZone.setAttribute('required', 'required');
                } else {
                    editZoneSelection.style.display = 'none';
                    editZone.removeAttribute('required');
                }
            });
            
            // Initialize the edit form handling
            const editUserForm = document.getElementById('editUserForm');
            editUserForm.addEventListener('submit', function(e) {
                if (editRole.value === 'autorite-zone' && editZone.value === '') {
                    e.preventDefault();
                    alert('Veuillez sélectionner une zone de responsabilité pour l\'Autorité de Zone.');
                }
            });
        });
        
        // Dashboard Functions
        function initCharts() {
            // Permits by type chart
            const permitsCtx = document.getElementById('permitsChart').getContext('2d');
            const permitsChart = new Chart(permitsCtx, {
                type: 'pie',
                data: {
                    labels: ['Travaux à chaud', 'Travaux électriques', 'Travaux en hauteur', 'Espace confiné', 'Excavation'],
                    datasets: [{
                        data: [42, 35, 28, 20, 31],
                        backgroundColor: [
                            '#2563eb',
                            '#7c3aed',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Permits by zone chart
            const zonesCtx = document.getElementById('zonesChart').getContext('2d');
            const zonesChart = new Chart(zonesCtx, {
                type: 'bar',
                data: {
                    labels: ['Zone A', 'Zone B', 'Zone C', 'Zone D', 'Zone E'],
                    datasets: [{
                        label: 'Nombre de permis',
                        data: [48, 37, 26, 29, 16],
                        backgroundColor: '#2563eb',
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // Users by role chart
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            const usersChart = new Chart(usersCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Coordinateur', 'Coordinateur', 'Responsable HSE', 'Autorité de Zone', 'Responsable d\'Exécution'],
                    datasets: [{
                        data: [5, 12, 18, 25, 18],
                        backgroundColor: [
                            '#2563eb',
                            '#7c3aed',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function refreshDashboard() {
            // In a real application, this would fetch fresh data from the backend
            // For now, we'll just show a success message
            alert('Tableau de bord mis à jour !');
            
            // Reinitialize charts with new data (in a real app)
            initCharts();
        }

        // Coordinator Functions
        function refreshDemandes() {
            alert('Liste des demandes actualisée !');
        }

        function refreshPermis() {
            alert('Liste des permis actualisée !');
        }

        function searchDemandes() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchDemandes");
            filter = input.value.toUpperCase();
            table = document.getElementById("demandes-permis").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) {
                tr[i].style.display = "none";
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = "";
                            break;
                        }
                    }
                }
            }
        }

        function searchPermis() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchPermis");
            filter = input.value.toUpperCase();
            table = document.getElementById("liste-permis").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) {
                tr[i].style.display = "none";
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = "";
                            break;
                        }
                    }
                }
            }
        }

        function voirDemande(id) {
            alert(`Affichage des détails de la demande ${id}`);
        }

        function verifierConformite(id) {
            // Open the conformity verification popup
            document.getElementById('conformityDemandeId').value = id;
            document.getElementById('conformityStatus').value = '';
            document.getElementById('conformityRemarks').value = '';
            document.getElementById('conformityRemarksSection').style.display = 'none';
            openPopup('conformityPopup');
        }

        function rejeterDemande(id) {
            // Show popup for rejection with remarks
            const remarques = prompt(`Motif de rejet pour la demande ${id}:\n\nVeuillez préciser les raisons de non-conformité:`);
            
            if (remarques && remarques.trim()) {
                if (confirm(`Êtes-vous sûr de vouloir rejeter la demande ${id} ?\n\nMotif: ${remarques}`)) {
                    alert(`Demande ${id} rejetée et renvoyée à l'initiateur avec remarques:\n${remarques}`);
                    
                    // Remove the row from the table (simulate rejection)
                    const row = document.querySelector(`tr[data-id="${id}"]`);
                    if (row) {
                        row.style.backgroundColor = '#ffebee';
                        row.style.opacity = '0.6';
                        
                        // Update the actions to show it's rejected
                        const actionsCell = row.querySelector('.table-actions');
                        actionsCell.innerHTML = `
                            <span class="badge badge-danger">Rejetée</span>
                            <button class="btn-table btn-view" onclick="voirRemarques('${id}')">
                                <i class='bx bx-message-detail'></i>
                                Voir remarques
                            </button>
                        `;
                    }
                }
            } else if (remarques !== null) {
                alert('Veuillez saisir un motif de rejet.');
            }
        }

        function emettrePermis(id) {
            if (confirm(`Vérifier la conformité et émettre le permis pour la demande ${id} ?`)) {
                alert(`Permis émis avec succès pour la demande ${id}`);
                
                // Update the row to show it's been processed
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    row.style.backgroundColor = '#e8f5e8';
                    row.style.opacity = '0.6';
                    
                    // Update the actions to show it's been processed
                    const actionsCell = row.querySelector('.table-actions');
                    actionsCell.innerHTML = `
                        <span class="badge badge-success">Permis émis</span>
                        <button class="btn-table btn-view" onclick="voirPermis('${id}')">
                            <i class='bx bx-show'></i>
                            Voir permis
                        </button>
                    `;
                }
            }
        }

        function voirRemarques(id) {
            alert(`Remarques de rejet pour la demande ${id}:\n\n- Documents de sécurité incomplets\n- Analyse des risques insuffisante\n- Équipements de protection non spécifiés\n- Formation du personnel non documentée`);
        }

        function traiterDemande(id) {
            alert(`Traitement de la demande ${id}`);
            showSection('verification-conformite');
        }

        function voirPermis(id) {
            // Sample data - in real application, this would come from backend
            const permisData = {
                'PT-2024-001': {
                    demandeur: 'Ahmed Benali',
                    typeTravail: 'Travaux dont les dangers associés sont potentiellement dangereux',
                    zone: 'Zone A - Production',
                    entreprise: 'TechnoServices SARL',
                    responsable: 'Ahmed Benali',
                    dateDebut: '2024-01-20',
                    dateFin: '2024-01-22',
                    heureDebut: '08:00',
                    heureFin: '17:00',
                    description: 'Maintenance des équipements de production et mise à niveau des systèmes',
                    risques: 'Risques mécaniques, électriques, exposition aux produits chimiques',
                    mesures: 'Consignation des équipements, port des EPI, surveillance continue',
                    epi: 'Casque, gants, chaussures de sécurité, lunettes de protection',
                    declarePar: 'Ahmed Benali (Responsable d\'Exécution)',
                    confirmePar: 'Marie Dubois (Représentant Autorité Zone A)',
                    dateDeclaration: '2024-01-22T16:30',
                    dateConfirmation: '2024-01-22T17:15',
                    remarques: 'Travaux terminés conformément aux spécifications. Tous les équipements de sécurité ont été utilisés correctement. Zone nettoyée et sécurisée.'
                },
                'PT-2024-003': {
                    demandeur: 'Karim Messaoud',
                    typeTravail: 'Travaux d\'urgence',
                    zone: 'Zone C - Laboratoire',
                    entreprise: 'UrgenceRepair',
                    responsable: 'Karim Messaoud',
                    dateDebut: '2024-01-18',
                    dateFin: '2024-01-21',
                    heureDebut: '07:00',
                    heureFin: '19:00',
                    description: 'Réparation d\'urgence suite à fuite dans le système de refroidissement',
                    risques: 'Exposition aux fluides frigorigènes, risques de brûlures, intoxication',
                    mesures: 'Ventilation forcée, détecteurs de gaz, équipe de secours en standby',
                    epi: 'Combinaison étanche, masque respiratoire, gants cryogéniques',
                    declarePar: 'Karim Messaoud (Responsable d\'Exécution)',
                    confirmePar: 'Jean Martin (Représentant Autorité Zone C)',
                    dateDeclaration: '2024-01-21T14:45',
                    dateConfirmation: '2024-01-21T15:20',
                    remarques: 'Réparation d\'urgence terminée avec succès. Fuite colmatée et système testé. Laboratoire remis en service normal.'
                },
                'PT-2024-005': {
                    demandeur: 'Samira Khelifi',
                    typeTravail: 'Travaux routiniers à faibles risques',
                    zone: 'Zone B - Stockage',
                    entreprise: 'LogisticPro',
                    responsable: 'Samira Khelifi',
                    dateDebut: '2024-01-19',
                    dateFin: '2024-01-20',
                    heureDebut: '08:30',
                    heureFin: '16:30',
                    description: 'Réorganisation des espaces de stockage et mise à jour de l\'inventaire',
                    risques: 'Chute d\'objets, troubles musculo-squelettiques, collision avec équipements',
                    mesures: 'Port du casque, techniques de manutention, signalisation des zones',
                    epi: 'Casque, gants, chaussures de sécurité, ceinture de manutention',
                    declarePar: 'Samira Khelifi (Responsable d\'Exécution)',
                    confirmePar: 'Sophie Laurent (Représentant Autorité Zone B)',
                    dateDeclaration: '2024-01-20T12:00',
                    dateConfirmation: '2024-01-20T12:30',
                    remarques: 'Maintenance préventive effectuée selon planning. Tous les contrôles de sécurité validés. Équipements remis en service.'
                },
                'PT-2024-007': {
                    demandeur: 'Fatima Zohra',
                    typeTravail: 'Travaux dispensés du permis de travail',
                    zone: 'Zone D - Utilités',
                    entreprise: 'UtilitiesPro',
                    responsable: 'Fatima Zohra',
                    dateDebut: '2024-01-17',
                    dateFin: '2024-01-18',
                    heureDebut: '09:00',
                    heureFin: '15:00',
                    description: 'Maintenance de routine des systèmes d\'utilités et contrôles périodiques',
                    risques: 'Risques mineurs, exposition aux produits de nettoyage',
                    mesures: 'Port des EPI de base, ventilation naturelle, signalisation',
                    epi: 'Gants, lunettes de protection, chaussures de sécurité',
                    declarePar: 'Fatima Zohra (Responsable d\'Exécution)',
                    confirmePar: 'Pierre Durand (Représentant Autorité Zone D)',
                    dateDeclaration: '2024-01-18T10:15',
                    dateConfirmation: '2024-01-18T10:45',
                    remarques: 'Travaux de routine terminés sans incident. Inspection finale effectuée et approuvée. Zone remise en état normal.'
                }
            };

            const data = permisData[id];
            if (data) {
                // Clear any suspension info first
                const modalBody = document.querySelector('#permisPopup .modal-body');
                const suspensionInfo = modalBody.querySelector('.suspension-info');
                if (suspensionInfo) {
                    suspensionInfo.remove();
                }

                // Populate all fields with error handling
                const setFieldValue = (fieldId, value) => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.textContent = value || '-';
                    }
                };

                setFieldValue('permisId', id);
                setFieldValue('permisDemandeur', data.demandeur);
                setFieldValue('permisTypeTravail', data.typeTravail);
                setFieldValue('permisZone', data.zone);
                setFieldValue('permisEntreprise', data.entreprise);
                setFieldValue('permisResponsable', data.responsable);
                setFieldValue('permisDateDebut', data.dateDebut);
                setFieldValue('permisDateFin', data.dateFin);
                setFieldValue('permisHeureDebut', data.heureDebut);
                setFieldValue('permisHeureFin', data.heureFin);
                setFieldValue('permisDescription', data.description);
                setFieldValue('permisRisques', data.risques);
                setFieldValue('permisMesures', data.mesures);
                setFieldValue('permisEPI', data.epi);
                setFieldValue('permisDeclarePar', data.declarePar);
                setFieldValue('permisConfirmePar', data.confirmePar);
                setFieldValue('permisDateDeclaration', data.dateDeclaration);
                setFieldValue('permisDateConfirmation', data.dateConfirmation);
                setFieldValue('permisRemarques', data.remarques);
            }

            openPopup('permisPopup');
        }

        function prolongerPermis(id) {
            alert(`Prolongation du permis ${id}`);
            showSection('prolongation-permis');
        }

        function cloturerPermis(id) {
            if (confirm(`Êtes-vous sûr de vouloir clôturer le permis ${id} ?\n\nLe permis sera transféré au Responsable HSE pour finalisation.`)) {
                // Find the row and add visual feedback
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    // Disable the button immediately to prevent double clicks
                    const button = row.querySelector('.btn-delete');
                    if (button) {
                        button.disabled = true;
                        button.innerHTML = '<i class="bx bx-loader-alt bx-spin"></i> Clôture...';
                        button.style.opacity = '0.6';
                        button.style.pointerEvents = 'none';
                    }
                    
                    // Add success animation
                    row.style.transition = 'all 0.6s ease-out';
                    row.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    row.style.transform = 'scale(1.02)';
                    
                    // Show success message immediately
                    const successMsg = document.createElement('div');
                    successMsg.innerHTML = `
                        <div style="position: fixed; top: 20px; right: 20px; background: var(--success); color: white; 
                                    padding: 16px 24px; border-radius: var(--radius-lg); box-shadow: var(--shadow-lg); 
                                    z-index: 1002; display: flex; align-items: center; gap: 12px; font-weight: 600;
                                    animation: slideInRight 0.3s ease-out;">
                            <i class='bx bx-check-circle' style="font-size: 20px;"></i>
                            Permis ${id} clôturé avec succès !
                        </div>
                    `;
                    document.body.appendChild(successMsg);
                    
                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successMsg.remove();
                    }, 3000);
                    
                    // Fade out and remove the row immediately (reduced delay)
                    setTimeout(() => {
                        row.style.opacity = '0';
                        row.style.transform = 'scale(0.95) translateX(100px)';
                        setTimeout(() => {
                            row.remove();
                            updateDashboardCounters();
                        }, 300);
                    }, 200); // Reduced from 1000ms to 200ms for immediate response
                }
            }
        }

        // Functions for Permis Suspendus/Annulés section
        function voirPermisSuspendu(id) {
            // Sample data for suspended permits
            const permisSuspenduData = {
                'PT-2024-008': {
                    demandeur: 'Mohamed Larbi',
                    typeTravail: 'Travaux électriques',
                    zone: 'Zone A - Production',
                    entreprise: 'ElectroTech SARL',
                    responsable: 'Mohamed Larbi',
                    dateDebut: '2024-01-20',
                    dateFin: '2024-01-22',
                    heureDebut: '08:00',
                    heureFin: '17:00',
                    description: 'Installation et maintenance des équipements électriques de production',
                    risques: 'Risques électriques, électrocution, incendie',
                    mesures: 'Consignation électrique, vérification absence de tension, port des EPI',
                    epi: 'Casque isolant, gants diélectriques, chaussures de sécurité isolantes',
                    dateSuspension: '23/01/2024',
                    suspenduPar: 'Marie Sécurité (Responsable HSE)',
                    motif: 'Non-respect des consignes de sécurité',
                    statut: 'Suspendu/Annulé',
                    declarePar: 'Mohamed Larbi (Responsable d\'Exécution)',
                    confirmePar: 'Marie Dubois (Représentant Autorité Zone A)',
                    dateDeclaration: '2024-01-20T08:30',
                    dateConfirmation: '2024-01-20T09:15',
                    remarques: 'PERMIS SUSPENDU - Non-respect des consignes de sécurité. Travaux interrompus immédiatement pour mise en conformité des équipements de protection.'
                },
                'PT-2024-009': {
                    demandeur: 'Aicha Benali',
                    typeTravail: 'Travaux de maintenance',
                    zone: 'Zone B - Stockage',
                    entreprise: 'MaintenancePro',
                    responsable: 'Aicha Benali',
                    dateDebut: '2024-01-21',
                    dateFin: '2024-01-23',
                    heureDebut: '09:00',
                    heureFin: '16:00',
                    description: 'Maintenance préventive des équipements de stockage et manutention',
                    risques: 'Chute d\'objets, troubles musculo-squelettiques, collision',
                    mesures: 'Balisage de zone, techniques de manutention, surveillance',
                    epi: 'Casque, gants, chaussures de sécurité, ceinture de manutention',
                    dateSuspension: '24/01/2024',
                    suspenduPar: 'Jean Autorité (Représentant autorité zone)',
                    motif: 'Conditions météorologiques dangereuses',
                    statut: 'Suspendu/Annulé',
                    declarePar: 'Aicha Benali (Responsable d\'Exécution)',
                    confirmePar: 'Jean Martin (Représentant Autorité Zone B)',
                    dateDeclaration: '2024-01-21T10:00',
                    dateConfirmation: '2024-01-21T10:30',
                    remarques: 'PERMIS ANNULÉ - Conditions météorologiques dangereuses (vents forts). Travaux reportés pour sécurité du personnel.'
                },
                'PT-2024-010': {
                    demandeur: 'Rachid Khelifi',
                    typeTravail: 'Travaux en hauteur',
                    zone: 'Zone C - Laboratoire',
                    entreprise: 'HauteurSecure',
                    responsable: 'Rachid Khelifi',
                    dateDebut: '2024-01-22',
                    dateFin: '2024-01-24',
                    heureDebut: '07:30',
                    heureFin: '15:30',
                    description: 'Travaux de maintenance en hauteur sur les systèmes de ventilation',
                    risques: 'Chute de hauteur, chute d\'objets, exposition aux intempéries',
                    mesures: 'Harnais de sécurité, ligne de vie, balisage au sol',
                    epi: 'Harnais complet, casque, gants antidérapants, chaussures de sécurité',
                    dateSuspension: '25/01/2024',
                    suspenduPar: 'Pierre Représentant (Représentant HSE)',
                    motif: 'Équipement de protection défaillant',
                    statut: 'Suspendu/Annulé',
                    declarePar: 'Rachid Khelifi (Responsable d\'Exécution)',
                    confirmePar: 'Sophie Laurent (Représentant Autorité Zone C)',
                    dateDeclaration: '2024-01-22T14:00',
                    dateConfirmation: '2024-01-22T14:45',
                    remarques: 'PERMIS SUSPENDU - Équipement de protection défaillant détecté lors de l\'inspection. Travaux suspendus jusqu\'à remplacement des EPI.'
                },
                'PT-2024-011': {
                    demandeur: 'Nadia Hamidi',
                    typeTravail: 'Travaux de soudage',
                    zone: 'Zone D - Maintenance',
                    dateDebut: '2024-01-23',
                    dateFin: '2024-01-25',
                    dateSuspension: '26/01/2024',
                    suspenduPar: 'Marie Sécurité (Responsable HSE)',
                    motif: 'Risque d\'incendie élevé',
                    statut: 'Suspendu/Annulé',
                    declarePar: 'Nadia Hamidi (Responsable d\'Exécution)',
                    confirmePar: 'Pierre Durand (Représentant Autorité Zone D)',
                    dateDeclaration: '2024-01-23T09:00',
                    dateConfirmation: '2024-01-23T09:30',
                    remarques: 'PERMIS ANNULÉ - Risque d\'incendie élevé en raison de la proximité de matières inflammables. Travaux reportés après sécurisation de la zone.'
                },
                'PT-2024-012': {
                    demandeur: 'Karim Messaoud',
                    typeTravail: 'Travaux de peinture',
                    zone: 'Zone E - Bureaux',
                    dateDebut: '2024-01-24',
                    dateFin: '2024-01-26',
                    dateSuspension: '27/01/2024',
                    suspenduPar: 'Sophie Contrôle (Représentant HSE)',
                    motif: 'Ventilation insuffisante',
                    statut: 'Suspendu/Annulé',
                    declarePar: 'Karim Messaoud (Responsable d\'Exécution)',
                    confirmePar: 'Marie Dubois (Représentant Autorité Zone E)',
                    dateDeclaration: '2024-01-24T11:00',
                    dateConfirmation: '2024-01-24T11:30',
                    remarques: 'PERMIS SUSPENDU - Ventilation insuffisante pour travaux de peinture. Travaux suspendus jusqu\'à amélioration du système de ventilation.'
                }
            };

            const data = permisSuspenduData[id];
            if (data) {
                // Clear any suspension info first
                const modalBody = document.querySelector('#permisPopup .modal-body');
                const existingSuspensionInfo = modalBody.querySelector('.suspension-info');
                if (existingSuspensionInfo) {
                    existingSuspensionInfo.remove();
                }

                // Populate all permit popup fields with error handling
                const setFieldValue = (fieldId, value) => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.textContent = value || '-';
                    }
                };

                setFieldValue('permisId', id);
                setFieldValue('permisDemandeur', data.demandeur);
                setFieldValue('permisTypeTravail', data.typeTravail);
                setFieldValue('permisZone', data.zone);
                setFieldValue('permisEntreprise', data.entreprise);
                setFieldValue('permisResponsable', data.responsable);
                setFieldValue('permisDateDebut', data.dateDebut);
                setFieldValue('permisDateFin', data.dateFin);
                setFieldValue('permisHeureDebut', data.heureDebut);
                setFieldValue('permisHeureFin', data.heureFin);
                setFieldValue('permisDescription', data.description);
                setFieldValue('permisRisques', data.risques);
                setFieldValue('permisMesures', data.mesures);
                setFieldValue('permisEPI', data.epi);
                setFieldValue('permisDeclarePar', data.declarePar);
                setFieldValue('permisConfirmePar', data.confirmePar);
                setFieldValue('permisDateDeclaration', data.dateDeclaration);
                setFieldValue('permisDateConfirmation', data.dateConfirmation);
                setFieldValue('permisRemarques', data.remarques);

                // Add suspension information to the popup
                let suspensionInfo = modalBody.querySelector('.suspension-info');
                if (!suspensionInfo) {
                    suspensionInfo = document.createElement('div');
                    suspensionInfo.className = 'suspension-info';
                    modalBody.insertBefore(suspensionInfo, modalBody.firstChild);
                }

                suspensionInfo.innerHTML = `
                    <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.2);
                                border-radius: var(--radius-md); padding: 16px; margin-bottom: 24px;">
                        <h3 style="color: var(--danger); margin-bottom: 12px; display: flex; align-items: center; gap: 8px;">
                            <i class='bx bx-block'></i>
                            Informations de suspension/annulation
                        </h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 12px;">
                            <div><strong>Date de suspension:</strong> ${data.dateSuspension}</div>
                            <div><strong>Suspendu par:</strong> ${data.suspenduPar}</div>
                            <div><strong>Statut:</strong> <span class="badge badge-danger">${data.statut}</span></div>
                            <div style="grid-column: 1 / -1;"><strong>Motif:</strong> ${data.motif}</div>
                        </div>
                    </div>
                `;

                openPopup('permisPopup');
            }
        }

        function cloturerPermisSuspendu(id) {
            if (confirm(`Êtes-vous sûr de vouloir clôturer définitivement le permis suspendu ${id} ?\n\nCette action est irréversible et le permis sera archivé.`)) {
                // Find the row and add visual feedback
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    // Disable the button immediately to prevent double clicks
                    const button = row.querySelector('.btn-delete');
                    if (button) {
                        button.disabled = true;
                        button.innerHTML = '<i class="bx bx-loader-alt bx-spin"></i> Clôture...';
                        button.style.opacity = '0.6';
                        button.style.pointerEvents = 'none';
                    }

                    // Add success animation
                    row.style.transition = 'all 0.6s ease-out';
                    row.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    row.style.transform = 'scale(1.02)';

                    // Show success message immediately
                    const successMsg = document.createElement('div');
                    successMsg.innerHTML = `
                        <div style="position: fixed; top: 20px; right: 20px; background: var(--success); color: white;
                                    padding: 16px 24px; border-radius: var(--radius-lg); box-shadow: var(--shadow-lg);
                                    z-index: 1002; display: flex; align-items: center; gap: 12px; font-weight: 600;
                                    animation: slideInRight 0.3s ease-out;">
                            <i class='bx bx-check-circle' style="font-size: 20px;"></i>
                            Permis suspendu ${id} clôturé et archivé avec succès !
                        </div>
                    `;
                    document.body.appendChild(successMsg);

                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successMsg.remove();
                    }, 3000);

                    // Fade out and remove the row
                    setTimeout(() => {
                        row.style.opacity = '0';
                        row.style.transform = 'scale(0.95) translateX(100px)';
                        setTimeout(() => {
                            row.remove();
                            // Update dashboard counters if function exists
                            if (typeof updateDashboardCounters === 'function') {
                                updateDashboardCounters();
                            }
                        }, 300);
                    }, 200);
                }
            }
        }

        function refreshPermisSuspendus() {
            // Simulate refresh with loading animation
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bx bx-loader-alt bx-spin"></i> Actualisation...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // Show refresh success message
                const successMsg = document.createElement('div');
                successMsg.innerHTML = `
                    <div style="position: fixed; top: 20px; right: 20px; background: var(--info); color: white;
                                padding: 12px 20px; border-radius: var(--radius-md); box-shadow: var(--shadow-lg);
                                z-index: 1002; display: flex; align-items: center; gap: 8px; font-weight: 500;
                                animation: slideInRight 0.3s ease-out;">
                        <i class='bx bx-refresh' style="font-size: 16px;"></i>
                        Liste des permis suspendus actualisée
                    </div>
                `;
                document.body.appendChild(successMsg);

                setTimeout(() => {
                    successMsg.remove();
                }, 2000);
            }, 1000);
        }

        function refreshProlongations() {
            // Simulate refresh with loading animation
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bx bx-loader-alt bx-spin"></i> Actualisation...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;

                // Show refresh success message
                const successMsg = document.createElement('div');
                successMsg.innerHTML = `
                    <div style="position: fixed; top: 20px; right: 20px; background: var(--info); color: white;
                                padding: 12px 20px; border-radius: var(--radius-md); box-shadow: var(--shadow-lg);
                                z-index: 1002; display: flex; align-items: center; gap: 8px; font-weight: 500;
                                animation: slideInRight 0.3s ease-out;">
                        <i class='bx bx-refresh' style="font-size: 16px;"></i>
                        Liste des prolongations actualisée
                    </div>
                `;
                document.body.appendChild(successMsg);

                setTimeout(() => {
                    successMsg.remove();
                }, 2000);
            }, 1000);
        }

        function searchPermisSuspendus() {
            const input = document.getElementById('searchPermisSuspendus');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('permisSuspendusTableBody');
            const rows = table.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }

        function searchProlongations() {
            const input = document.getElementById('searchProlongations');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('prolongationsTableBody');
            const rows = table.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        }

        function toggleRemarques() {
            const statut = document.getElementById('statutConformite').value;
            const remarquesSection = document.getElementById('remarquesSection');
            
            if (statut === 'non-conforme') {
                remarquesSection.style.display = 'block';
                document.getElementById('remarques').setAttribute('required', 'required');
            } else {
                remarquesSection.style.display = 'none';
                document.getElementById('remarques').removeAttribute('required');
            }
        }

        function validerConformite() {
            const statut = document.getElementById('statutConformite').value;
            if (!statut) {
                alert('Veuillez sélectionner le statut de conformité');
                return;
            }
            
            if (statut === 'non-conforme') {
                const remarques = document.getElementById('remarques').value;
                if (!remarques.trim()) {
                    alert('Veuillez saisir les remarques de non-conformité');
                    return;
                }
            }
            
            alert(`Vérification validée avec le statut: ${statut}`);
            showSection('demandes-permis');
        }

        function annulerVerification() {
            document.getElementById('statutConformite').value = '';
            document.getElementById('remarques').value = '';
            document.getElementById('remarquesSection').style.display = 'none';
            showSection('demandes-permis');
        }

        function annulerEmission() {
            document.getElementById('emissionPermisForm').reset();
            showSection('demandes-permis');
        }

        function annulerProlongation() {
            document.getElementById('prolongationForm').reset();
            showSection('permis-travail');
        }

        function annulerCloture() {
            document.getElementById('clotureForm').reset();
            showSection('permis-travail');
        }



        function updateDashboardCounters() {
            // Update dashboard statistics after permit closure
            const remainingPermits = document.querySelectorAll('#permisTableBody tr').length;
            console.log(`Permis restants: ${remainingPermits}`);
            // In a real application, this would update the dashboard counters
        }

        // Missing utility functions
        function refreshDemandes() {
            alert('Liste des demandes actualisée !');
            // In real application, this would reload data from server
        }

        function refreshPermis() {
            alert('Liste des permis actualisée !');
            // In real application, this would reload data from server
        }

        function refreshDashboard() {
            alert('Tableau de bord actualisé !');
            // In real application, this would reload dashboard data
        }

        function searchDemandes() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchDemandes");
            filter = input.value.toUpperCase();
            table = document.getElementById("demandes-permis").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) {
                tr[i].style.display = "none";
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = "";
                            break;
                        }
                    }
                }
            }
        }

        function searchPermis() {
            let input, filter, table, tr, td, i, txtValue;
            input = document.getElementById("searchPermis");
            filter = input.value.toUpperCase();
            table = document.getElementById("permis-travail").querySelector("table");
            tr = table.getElementsByTagName("tr");

            for (i = 1; i < tr.length; i++) {
                tr[i].style.display = "none";
                td = tr[i].getElementsByTagName("td");
                for (let j = 0; j < td.length; j++) {
                    if (td[j]) {
                        txtValue = td[j].textContent || td[j].innerText;
                        if (txtValue.toUpperCase().indexOf(filter) > -1) {
                            tr[i].style.display = "";
                            break;
                        }
                    }
                }
            }
        }

        // Form submission handlers
        document.addEventListener('DOMContentLoaded', function() {
            // Emission permis form
            const emissionForm = document.getElementById('emissionPermisForm');
            if (emissionForm) {
                emissionForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Permis émis avec succès !');
                    showSection('permis-travail');
                });
            }

            // Prolongation form
            const prolongationForm = document.getElementById('prolongationForm');
            if (prolongationForm) {
                prolongationForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Prolongation approuvée avec succès !');
                    showSection('permis-travail');
                });
            }

            // Cloture form
            const clotureForm = document.getElementById('clotureForm');
            if (clotureForm) {
                clotureForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('Permis clôturé avec succès !');
                    showSection('permis-travail');
                });

                // Handle conformity radio buttons
                const conformiteRadios = document.querySelectorAll('input[name="conformite"]');
                conformiteRadios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        const ecartsSection = document.getElementById('ecartsSection');
                        if (this.value === 'non') {
                            ecartsSection.style.display = 'block';
                            document.getElementById('descriptionEcarts').setAttribute('required', 'required');
                        } else {
                            ecartsSection.style.display = 'none';
                            document.getElementById('descriptionEcarts').removeAttribute('required');
                        }
                    });
                });
            }

            // Date calculations for prolongation
            const nouvelleDateFin = document.getElementById('nouvelleDateFin');
            const dateFinActuelle = document.getElementById('dateFinActuelle');
            const dureeProlongation = document.getElementById('dureeProlongation');

            if (nouvelleDateFin && dateFinActuelle && dureeProlongation) {
                nouvelleDateFin.addEventListener('change', function() {
                    const dateActuelle = new Date(dateFinActuelle.value);
                    const nouvelleDate = new Date(this.value);
                    
                    if (nouvelleDate > dateActuelle) {
                        const diffTime = Math.abs(nouvelleDate - dateActuelle);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        dureeProlongation.value = `${diffDays} jour(s)`;
                    } else {
                        dureeProlongation.value = '';
                    }
                });
            }

            // Conformity verification form
            const conformityForm = document.getElementById('conformityForm');
            if (conformityForm) {
                conformityForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const demandeId = document.getElementById('conformityDemandeId').value;
                    const status = document.getElementById('conformityStatus').value;
                    const remarks = document.getElementById('conformityRemarks').value;
                    
                    if (status === 'non-conforme' && !remarks.trim()) {
                        alert('Veuillez saisir les remarques de non-conformité');
                        return;
                    }
                    
                    alert(`Vérification validée pour ${demandeId} avec le statut: ${status}`);
                    closePopup('conformityPopup');
                    
                    // Automatically return to the table - user can now choose to reject or emit
                    showSection('demandes-permis');
                });

                // Handle conformity status change
                document.getElementById('conformityStatus').addEventListener('change', function() {
                    const remarksSection = document.getElementById('conformityRemarksSection');
                    if (this.value === 'non-conforme') {
                        remarksSection.style.display = 'block';
                        document.getElementById('conformityRemarks').setAttribute('required', 'required');
                    } else {
                        remarksSection.style.display = 'none';
                        document.getElementById('conformityRemarks').removeAttribute('required');
                    }
                });
            }
        });

        // Functions for Prolongation section
        function voirDemandeProlongation(id) {
            // Sample data for prolongation requests
            const prolongationData = {
                'PT-2024-001': {
                    demandeur: 'Ahmed Benali',
                    typeTravail: 'Travaux dont les dangers associés sont potentiellement dangereux',
                    zone: 'Zone A - Production',
                    entreprise: 'TechnoServices SARL',
                    responsable: 'Ahmed Benali',
                    dateDebut: '2024-01-20',
                    dateFin: '2024-01-22',
                    heureDebut: '08:00',
                    heureFin: '17:00',
                    description: 'Maintenance des équipements de production et mise à niveau des systèmes',
                    risques: 'Risques mécaniques, électriques, exposition aux produits chimiques',
                    mesures: 'Consignation des équipements, port des EPI, surveillance continue',
                    epi: 'Casque, gants, chaussures de sécurité, lunettes de protection',
                    declarePar: 'Ahmed Benali (Responsable d\'Exécution)',
                    confirmePar: 'Marie Dubois (Représentant Autorité Zone A)',
                    dateDeclaration: '2024-01-22T16:30',
                    dateConfirmation: '2024-01-22T17:15',
                    remarques: 'DEMANDE DE PROLONGATION - Travaux nécessitant une extension de 2 jours pour finalisation complète des tests de sécurité.'
                },
                'PT-2024-003': {
                    demandeur: 'Karim Messaoud',
                    typeTravail: 'Travaux d\'urgence',
                    zone: 'Zone C - Laboratoire',
                    entreprise: 'UrgenceRepair',
                    responsable: 'Karim Messaoud',
                    dateDebut: '2024-01-18',
                    dateFin: '2024-01-21',
                    heureDebut: '07:00',
                    heureFin: '19:00',
                    description: 'Réparation d\'urgence suite à fuite dans le système de refroidissement',
                    risques: 'Exposition aux fluides frigorigènes, risques de brûlures, intoxication',
                    mesures: 'Ventilation forcée, détecteurs de gaz, équipe de secours en standby',
                    epi: 'Combinaison étanche, masque respiratoire, gants cryogéniques',
                    declarePar: 'Karim Messaoud (Responsable d\'Exécution)',
                    confirmePar: 'Jean Martin (Représentant Autorité Zone C)',
                    dateDeclaration: '2024-01-21T14:45',
                    dateConfirmation: '2024-01-21T15:20',
                    remarques: 'DEMANDE DE PROLONGATION - Extension nécessaire pour tests complets du système réparé et validation par bureau d\'études.'
                },
                'PT-2024-005': {
                    demandeur: 'Yacine Boumediene',
                    typeTravail: 'Travaux routiniers à faibles risques',
                    zone: 'Zone B - Stockage',
                    entreprise: 'LogisticPro',
                    responsable: 'Yacine Boumediene',
                    dateDebut: '2024-01-19',
                    dateFin: '2024-01-20',
                    heureDebut: '08:30',
                    heureFin: '16:30',
                    description: 'Réorganisation des espaces de stockage et mise à jour de l\'inventaire',
                    risques: 'Chute d\'objets, troubles musculo-squelettiques, collision avec équipements',
                    mesures: 'Port du casque, techniques de manutention, signalisation des zones',
                    epi: 'Casque, gants, chaussures de sécurité, ceinture de manutention',
                    declarePar: 'Yacine Boumediene (Responsable d\'Exécution)',
                    confirmePar: 'Sophie Laurent (Représentant Autorité Zone B)',
                    dateDeclaration: '2024-01-20T12:00',
                    dateConfirmation: '2024-01-20T12:30',
                    remarques: 'DEMANDE DE PROLONGATION - Travaux plus complexes que prévu, nécessitant une journée supplémentaire pour finalisation.'
                }
            };

            const data = prolongationData[id];
            if (data) {
                // Clear any suspension info
                const modalBody = document.querySelector('#permisPopup .modal-body');
                const suspensionInfo = modalBody.querySelector('.suspension-info');
                if (suspensionInfo) {
                    suspensionInfo.remove();
                }

                // Populate all fields with error handling
                const setFieldValue = (fieldId, value) => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.textContent = value || '-';
                    }
                };

                setFieldValue('permisId', id);
                setFieldValue('permisDemandeur', data.demandeur);
                setFieldValue('permisTypeTravail', data.typeTravail);
                setFieldValue('permisZone', data.zone);
                setFieldValue('permisEntreprise', data.entreprise);
                setFieldValue('permisResponsable', data.responsable);
                setFieldValue('permisDateDebut', data.dateDebut);
                setFieldValue('permisDateFin', data.dateFin);
                setFieldValue('permisHeureDebut', data.heureDebut);
                setFieldValue('permisHeureFin', data.heureFin);
                setFieldValue('permisDescription', data.description);
                setFieldValue('permisRisques', data.risques);
                setFieldValue('permisMesures', data.mesures);
                setFieldValue('permisEPI', data.epi);
                setFieldValue('permisDeclarePar', data.declarePar);
                setFieldValue('permisConfirmePar', data.confirmePar);
                setFieldValue('permisDateDeclaration', data.dateDeclaration);
                setFieldValue('permisDateConfirmation', data.dateConfirmation);
                setFieldValue('permisRemarques', data.remarques);

                openPopup('permisPopup');
            }
        }

        function emettreProlongation(id) {
            if (confirm(`Confirmer l'émission de la prolongation pour le permis ${id} ?\n\nLe permis sera prolongé selon les conditions approuvées.`)) {
                // Find the row and add visual feedback
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    // Disable the button immediately to prevent double clicks
                    const button = row.querySelector('.btn-view[onclick*="emettreProlongation"]');
                    if (button) {
                        button.disabled = true;
                        button.innerHTML = '<i class="bx bx-loader-alt bx-spin"></i> Émission...';
                        button.style.opacity = '0.6';
                        button.style.pointerEvents = 'none';
                    }

                    // Add success animation
                    row.style.transition = 'all 0.6s ease-out';
                    row.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    row.style.transform = 'scale(1.02)';

                    // Show success message immediately
                    const successMsg = document.createElement('div');
                    successMsg.innerHTML = `
                        <div style="position: fixed; top: 20px; right: 20px; background: var(--success); color: white;
                                    padding: 16px 24px; border-radius: var(--radius-lg); box-shadow: var(--shadow-lg);
                                    z-index: 1002; display: flex; align-items: center; gap: 12px; font-weight: 600;
                                    animation: slideInRight 0.3s ease-out;">
                            <i class='bx bx-check-circle' style="font-size: 20px;"></i>
                            Prolongation émise avec succès pour le permis ${id} !
                        </div>
                    `;
                    document.body.appendChild(successMsg);

                    // Remove success message after 3 seconds
                    setTimeout(() => {
                        successMsg.remove();
                    }, 3000);

                    // Fade out and remove the row
                    setTimeout(() => {
                        row.style.opacity = '0';
                        row.style.transform = 'scale(0.95) translateX(100px)';
                        setTimeout(() => {
                            row.remove();
                            // Update dashboard counters if function exists
                            if (typeof updateDashboardCounters === 'function') {
                                updateDashboardCounters();
                            }
                        }, 300);
                    }, 200);
                }
            }
        }
    </script>
</body>
</html>